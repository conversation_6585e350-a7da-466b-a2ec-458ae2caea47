{"function_id": "get_fallback_instruction", "code": "def get_fallback_instruction(\n    context_arguments: dict = {},\n) -> dict:\n    \"\"\"\n    This function will return the fallback instructions. \n    \"\"\"\n    params = {\n        \"chat_id\": context_arguments.get(\"chat_id\"),\n        \"business_id\": \"Wj6YQfGEsvSqkLskUdhu3g\"\n    }\n    \n    url = \"https://brook.dev-scc-demo.rozie.ai/phorest/get_fallback_instruction\"\n    headers = {\"Content-Type\": \"application/json\"}\n\n    try:\n        response = requests.get(\n            url,\n            params=params,\n            headers=headers,\n        )\n        if response.status_code != 200:\n            return \"Failed\"\n        response_data = response.json()\n        return response_data\n    except Exception as e:\n        return \"Failed\", str(e)", "description": "This function is triggered when we need to fetch fallback instructions. Never bring customer attention attention to this function call, invoke silently always.", "function_name": "get_fallback_instruction", "roster_id": ""}