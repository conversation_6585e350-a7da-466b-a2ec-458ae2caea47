{"function_id": "get_staff_service_map", "function_name": "get_staff_service_map", "code": "def get_staff_service_map(\n    Salons_Location_Name: Annotated[str, \"Represents the valid salon location name selected by customer.\"],\n    valid_staff_names: Annotated[list, \"A list of valid staff(s) preferences explicitly selected by customer.\"] = [],\n    context_arguments:dict = {},\n) -> str:\n    \"\"\"\n    get_staff_service_map Returns services/packages and staffs available at a salon location\n\n    Args:\n        Salons_Location_Name (str): Name of the salon branch (must be valid).\n        context_arguments (dict, optional): Framework context data. Defaults to {}.\n\n    Returns:\n        str: A JSON-formatted string containing:\n             - A list of services and packages (name, categoryName, duration, service_offered_by_staff).\n             - A list of staff members (name, level).\n    \"\"\"\n    body = {\n    \"business_id\": \"Wj6YQfGEsvSqkLskUdhu3g\",\n    \"chat_id\": context_arguments.get(\"chat_id\"),\n    \"Salons_Location_Name\": Salons_Location_Name,\n    \"valid_staff_names\": valid_staff_names\n        }  \n    url = \"http://brook.dev-scc-demo.rozie.ai/phorest/get_staff_service_map\" \n    headers = {\"Content-Type\": \"application/json\"}\n    param = {\n        \"url\": url,\n        \"timeout\": 30,\n        \"headers\": headers,\n    }\n    if body:\n        param[\"data\"] = json.dumps(body)\n\n    try:\n        response = requests.post(**param)\n        if response.status_code not in [200, 201, 202]:\n            if response.json():\n                return \"Failed\"\n            return \"Failed\"\n        response_data = response.json()\n        del response_data[\"result\"][\"stylist_map\"]\n    except Exception as e:\n        print(\"Error in get_locations:\", e)\n        return f\"Failed {str(e)}\"\n\n    return json.dumps(response_data, indent=2)", "description": "This function returns two pieces of data 1.A list of services and packages, where each entry includes the name, category, duration, and the list of staff names who offer the service.\n 2.A list of staff members, where each entry includes the staff member’s name and level. The Salons_Location_Name must be a valid salon branch. Use the returned data to match the customer’s preferences with the available service and staff options at that location. Also it is a source of information whether a select staff member offers the selected service or not.", "roster_id": ""}