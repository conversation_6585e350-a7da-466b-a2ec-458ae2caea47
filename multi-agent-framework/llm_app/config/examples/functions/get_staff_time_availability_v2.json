{"function_id": "get_staff_time_availability_v2", "code": "def get_staff_time_availability_v2(\n    Salons_Branch_Name: Annotated[str, \"This parameter specifies the location of the selected salon branch.\"],\n    Service: Annotated[list, \"A list of services explicitly selected by the customer from the valid options returned by function `get_staff_service_map`. This will filter salon locations based on the services offered at each location.\"],\n    Preferred_Date: Annotated[str, \"Customer preferred date for appointment in `%Y-%m-%d` format strictly.\"] ,\n    Staff_Name: Annotated[str, \"A staff's full name to filter based on staff availability\"] = \"\",\n    context_arguments: dict = {}  \n) -> str:  \n    body = {\n        \"business_id\": \"Wj6YQfGEsvSqkLskUdhu3g\",\n        \"chat_id\": context_arguments.get(\"chat_id\"),\n        \"Salons_Branch_Name\": Salons_Branch_Name,\n        \"Salon_Service\": Service,\n        \"Stylist_Name\": Staff_Name,\n        \"Preferred_Date\": Preferred_Date\n    }  \n    url = \"http://brook.dev-scc-demo.rozie.ai/phorest/get_available_slots_v2\" \n    headers = {\"Content-Type\": \"application/json\"}\n    param = {\n        \"url\": url,\n        \"timeout\": 30,\n        \"headers\": headers,\n    }\n    if body:\n        param[\"data\"] = json.dumps(body)\n    try:\n        response = requests.post(**param)\n        print(\"response:\", response.text)\n        if response.status_code not in [200, 201, 202]:\n            if response.json():\n                return \"Failed\"\n            return \"Failed\"\n        response_data = response.json()\n    except Exception as e:\n        print(\"Error in get_staff_time_availability :\", e)\n        return f\"Failed {str(e)}\"\n\n    return response_data", "description": "To check the availability of staff, use this function which retrieve a list of available appointment slots. If you want to view slots for a specific staff, apply the Staff_Name filter, ensuring the results are limited to that staff. The Staff_Name should be a valid full name obtained through the get_salon_stylists function. Additionally, you can use the Preferred_Date filter to specify a particular date for which you wish to check availability. The function returns a comprehensive list of appointment slots, each providing details such as the start time, end time, staff name, and the schedule for every service. This makes it easier to identify and book suitable appointments tailored to your preferences and availability. perquisite: Validate selected service(s) are offered by the selected staff member.", "function_name": "get_staff_time_availability_v2", "roster_id": ""}