{"function_id": "get_stylist_details", "code": "def get_stylist_details(\n    Salons_Branch_Name: Annotated[str, \"This parameter specifies the location selected by customer.\"],\n    Stylist_Name: Annotated[list, \"Represents a list of valid stylist names whose details need to be retrieved.\"],\n    context_arguments:dict = {},\n) -> str:\n\n    body = {\n    \"business_id\": \"Wj6YQfGEsvSqkLskUdhu3g\",\n    \"chat_id\": context_arguments.get(\"chat_id\"),\n    \"Salons_Branch_Name\": Salons_Branch_Name,\n    \"Stylist_Names\": Stylist_Name\n\t}  \n    url = \"http://brook.dev-scc-demo.rozie.ai/phorest/get_stylist_details\" \n    headers = {\"Content-Type\": \"application/json\"}\n    param = {\n        \"url\": url,\n        \"timeout\": 30,\n        \"headers\": headers,\n    }\n    if body:\n        param[\"data\"] = json.dumps(body)\n\n    try:\n        response = requests.post(**param)\n        if response.status_code not in [200, 201, 202]:\n            if response.json():\n                return \"Failed\"\n            return \"Failed\"\n        response_data = response.json()\n    except Exception as e:\n        print(\"Error in get_locations:\", e)\n        return f\"Failed {str(e)}\"\n\n    return json.dumps(response_data, indent=2)\n", "description": "The get_stylist_details function provides information about stylists, including their level or seniority, which reflects their experience, along with other relevant details.", "function_name": "get_stylist_details"}