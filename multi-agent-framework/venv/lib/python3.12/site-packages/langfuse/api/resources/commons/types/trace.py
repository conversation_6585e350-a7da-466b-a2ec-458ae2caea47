# This file was auto-generated by Fern from our API Definition.

import datetime as dt
import typing

from ....core.datetime_utils import serialize_datetime
from ....core.pydantic_utilities import deep_union_pydantic_dicts, pydantic_v1


class Trace(pydantic_v1.BaseModel):
    id: str = pydantic_v1.Field()
    """
    The unique identifier of a trace
    """

    timestamp: dt.datetime = pydantic_v1.Field()
    """
    The timestamp when the trace was created
    """

    name: typing.Optional[str] = pydantic_v1.Field(default=None)
    """
    The name of the trace
    """

    input: typing.Optional[typing.Any] = pydantic_v1.Field(default=None)
    """
    The input data of the trace. Can be any JSON.
    """

    output: typing.Optional[typing.Any] = pydantic_v1.Field(default=None)
    """
    The output data of the trace. Can be any JSON.
    """

    session_id: typing.Optional[str] = pydantic_v1.Field(alias="sessionId", default=None)
    """
    The session identifier associated with the trace
    """

    release: typing.Optional[str] = pydantic_v1.Field(default=None)
    """
    The release version of the application when the trace was created
    """

    version: typing.Optional[str] = pydantic_v1.Field(default=None)
    """
    The version of the trace
    """

    user_id: typing.Optional[str] = pydantic_v1.Field(alias="userId", default=None)
    """
    The user identifier associated with the trace
    """

    metadata: typing.Optional[typing.Any] = pydantic_v1.Field(default=None)
    """
    The metadata associated with the trace. Can be any JSON.
    """

    tags: typing.Optional[typing.List[str]] = pydantic_v1.Field(default=None)
    """
    The tags associated with the trace. Can be an array of strings or null.
    """

    public: typing.Optional[bool] = pydantic_v1.Field(default=None)
    """
    Public traces are accessible via url without login
    """

    def json(self, **kwargs: typing.Any) -> str:
        kwargs_with_defaults: typing.Any = {"by_alias": True, "exclude_unset": True, **kwargs}
        return super().json(**kwargs_with_defaults)

    def dict(self, **kwargs: typing.Any) -> typing.Dict[str, typing.Any]:
        kwargs_with_defaults_exclude_unset: typing.Any = {"by_alias": True, "exclude_unset": True, **kwargs}
        kwargs_with_defaults_exclude_none: typing.Any = {"by_alias": True, "exclude_none": True, **kwargs}

        return deep_union_pydantic_dicts(
            super().dict(**kwargs_with_defaults_exclude_unset), super().dict(**kwargs_with_defaults_exclude_none)
        )

    class Config:
        frozen = True
        smart_union = True
        allow_population_by_field_name = True
        populate_by_name = True
        extra = pydantic_v1.Extra.allow
        json_encoders = {dt.datetime: serialize_datetime}
