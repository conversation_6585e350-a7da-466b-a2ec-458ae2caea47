# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc
import warnings

from . import agent_worker_pb2 as agent__worker__pb2

GRPC_GENERATED_VERSION = '1.70.0'
GRPC_VERSION = grpc.__version__
_version_not_supported = False

try:
    from grpc._utilities import first_version_is_lower
    _version_not_supported = first_version_is_lower(GRPC_VERSION, GRPC_GENERATED_VERSION)
except ImportError:
    _version_not_supported = True

if _version_not_supported:
    raise RuntimeError(
        f'The grpc package installed is at version {GRPC_VERSION},'
        + f' but the generated code in agent_worker_pb2_grpc.py depends on'
        + f' grpcio>={GRPC_GENERATED_VERSION}.'
        + f' Please upgrade your grpc module to grpcio>={GRPC_GENERATED_VERSION}'
        + f' or downgrade your generated code using grpcio-tools<={GRPC_VERSION}.'
    )


class AgentRpcStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.OpenChannel = channel.stream_stream(
                '/agents.AgentRpc/OpenChannel',
                request_serializer=agent__worker__pb2.Message.SerializeToString,
                response_deserializer=agent__worker__pb2.Message.FromString,
                _registered_method=True)
        self.OpenControlChannel = channel.stream_stream(
                '/agents.AgentRpc/OpenControlChannel',
                request_serializer=agent__worker__pb2.ControlMessage.SerializeToString,
                response_deserializer=agent__worker__pb2.ControlMessage.FromString,
                _registered_method=True)
        self.RegisterAgent = channel.unary_unary(
                '/agents.AgentRpc/RegisterAgent',
                request_serializer=agent__worker__pb2.RegisterAgentTypeRequest.SerializeToString,
                response_deserializer=agent__worker__pb2.RegisterAgentTypeResponse.FromString,
                _registered_method=True)
        self.AddSubscription = channel.unary_unary(
                '/agents.AgentRpc/AddSubscription',
                request_serializer=agent__worker__pb2.AddSubscriptionRequest.SerializeToString,
                response_deserializer=agent__worker__pb2.AddSubscriptionResponse.FromString,
                _registered_method=True)
        self.RemoveSubscription = channel.unary_unary(
                '/agents.AgentRpc/RemoveSubscription',
                request_serializer=agent__worker__pb2.RemoveSubscriptionRequest.SerializeToString,
                response_deserializer=agent__worker__pb2.RemoveSubscriptionResponse.FromString,
                _registered_method=True)
        self.GetSubscriptions = channel.unary_unary(
                '/agents.AgentRpc/GetSubscriptions',
                request_serializer=agent__worker__pb2.GetSubscriptionsRequest.SerializeToString,
                response_deserializer=agent__worker__pb2.GetSubscriptionsResponse.FromString,
                _registered_method=True)


class AgentRpcServicer(object):
    """Missing associated documentation comment in .proto file."""

    def OpenChannel(self, request_iterator, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def OpenControlChannel(self, request_iterator, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def RegisterAgent(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def AddSubscription(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def RemoveSubscription(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetSubscriptions(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_AgentRpcServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'OpenChannel': grpc.stream_stream_rpc_method_handler(
                    servicer.OpenChannel,
                    request_deserializer=agent__worker__pb2.Message.FromString,
                    response_serializer=agent__worker__pb2.Message.SerializeToString,
            ),
            'OpenControlChannel': grpc.stream_stream_rpc_method_handler(
                    servicer.OpenControlChannel,
                    request_deserializer=agent__worker__pb2.ControlMessage.FromString,
                    response_serializer=agent__worker__pb2.ControlMessage.SerializeToString,
            ),
            'RegisterAgent': grpc.unary_unary_rpc_method_handler(
                    servicer.RegisterAgent,
                    request_deserializer=agent__worker__pb2.RegisterAgentTypeRequest.FromString,
                    response_serializer=agent__worker__pb2.RegisterAgentTypeResponse.SerializeToString,
            ),
            'AddSubscription': grpc.unary_unary_rpc_method_handler(
                    servicer.AddSubscription,
                    request_deserializer=agent__worker__pb2.AddSubscriptionRequest.FromString,
                    response_serializer=agent__worker__pb2.AddSubscriptionResponse.SerializeToString,
            ),
            'RemoveSubscription': grpc.unary_unary_rpc_method_handler(
                    servicer.RemoveSubscription,
                    request_deserializer=agent__worker__pb2.RemoveSubscriptionRequest.FromString,
                    response_serializer=agent__worker__pb2.RemoveSubscriptionResponse.SerializeToString,
            ),
            'GetSubscriptions': grpc.unary_unary_rpc_method_handler(
                    servicer.GetSubscriptions,
                    request_deserializer=agent__worker__pb2.GetSubscriptionsRequest.FromString,
                    response_serializer=agent__worker__pb2.GetSubscriptionsResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'agents.AgentRpc', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('agents.AgentRpc', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class AgentRpc(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def OpenChannel(request_iterator,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.stream_stream(
            request_iterator,
            target,
            '/agents.AgentRpc/OpenChannel',
            agent__worker__pb2.Message.SerializeToString,
            agent__worker__pb2.Message.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def OpenControlChannel(request_iterator,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.stream_stream(
            request_iterator,
            target,
            '/agents.AgentRpc/OpenControlChannel',
            agent__worker__pb2.ControlMessage.SerializeToString,
            agent__worker__pb2.ControlMessage.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def RegisterAgent(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/agents.AgentRpc/RegisterAgent',
            agent__worker__pb2.RegisterAgentTypeRequest.SerializeToString,
            agent__worker__pb2.RegisterAgentTypeResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def AddSubscription(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/agents.AgentRpc/AddSubscription',
            agent__worker__pb2.AddSubscriptionRequest.SerializeToString,
            agent__worker__pb2.AddSubscriptionResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def RemoveSubscription(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/agents.AgentRpc/RemoveSubscription',
            agent__worker__pb2.RemoveSubscriptionRequest.SerializeToString,
            agent__worker__pb2.RemoveSubscriptionResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetSubscriptions(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/agents.AgentRpc/GetSubscriptions',
            agent__worker__pb2.GetSubscriptionsRequest.SerializeToString,
            agent__worker__pb2.GetSubscriptionsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
