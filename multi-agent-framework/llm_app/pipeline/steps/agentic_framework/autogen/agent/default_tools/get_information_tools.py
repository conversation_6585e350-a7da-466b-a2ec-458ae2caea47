import re
import requests
from typing_extensions import Annotated

# ---External---
from bs4 import BeautifulSoup
from loguru import logger
from autogen_core.tools import FunctionTool

# ---Constants---
GET_INFORMATION_TOOL_DESCRIPTION = "function to get documents from knowledge base to answer the customer's question."


def get_information(
    question: Annotated[str, "question to retrieve answer from knowledge base."], context_arguments: dict = {}
) -> str:

    illuminar_config = context_arguments.get("illuminar_config", {})
    base_url = illuminar_config["base_url"]
    search_url = illuminar_config["search_url"]

    def get_searched_documents(result):
        return [clean_text(doc["description"]) for doc in result["data"]["attributes"]["search_results"]]

    def get_document_references(api_response):
        transformed_results = []

        if api_response.get("api_result_type") == "success":
            search_results = api_response.get("data", {}).get("attributes", {}).get("search_results", [])

            for result in search_results:
                transformed_results.append(
                    {
                        "doc_id": result.get("doc_id"),
                        "url": result.get("url", ""),
                        "title": result.get("title"),
                        "description": result.get("description"),
                        "description_html": result.get("description_html"),
                    }
                )

        return transformed_results

    def clean_text(text):
        if not text:
            return ""
        # Convert HTML to plain text
        soup = BeautifulSoup(text, "html.parser")
        # Remove all anchor tags (<a>) to exclude links and their content
        for a in soup.find_all("a"):
            a.decompose()
        text = soup.get_text()
        # Replace multiple newlines with a single newline
        text = re.sub(r"\n+", "\n", text)
        # Strip leading and trailing spaces and newlines
        return text.strip()

    documents = []
    reference = []
    headers = {
        "Content-Type": "application/json",
        "application-id": illuminar_config["application_id"],
    }
    if "api_key" in illuminar_config:
        headers["api-key"] = illuminar_config["api_key"]

    params = illuminar_config.get("params", {"num_of_results": 1})
    if "folders" in params:
        params["folders"] = ",".join(params["folders"])
    params["query"] = question
    logger.debug(f"params: {params}")
    if "num_of_results" in params:
        params["num_of_results"] = int(params["num_of_results"])
    if "threshold" in params:
        params["threshold"] = float(params["threshold"])
    try:
        response = requests.get(f"{base_url}{search_url}", params=params, headers=headers)
        logger.debug(f"response status {response.status_code}")

        response.raise_for_status()
        if response.status_code == 200:
            search_result = response.json()
            documents = get_searched_documents(search_result)
            reference = get_document_references(search_result)

    except requests.exceptions.RequestException as e:
        if isinstance(e, requests.exceptions.HTTPError):
            logger.exception(f"HTTP ERROR: {response.text}")
        elif isinstance(e, requests.exceptions.ConnectionError):
            logger.exception(f"Connection Error: Check your internet connection or the server status.")
        elif isinstance(e, requests.exceptions.Timeout):
            logger.exception(f"Timeout Error: The request took too long to respond.")
        elif isinstance(e, requests.exceptions.RequestException):
            logger.exception(f"General Request Exception:{e}")
        else:
            logger.exception(f"Error: {e}")

    return {"question": question, "references": reference, "documents": documents}


def return_tool():
    function_tool = FunctionTool(get_information, description=GET_INFORMATION_TOOL_DESCRIPTION)
    return {
        "tool_name": "get_information",
        "description": GET_INFORMATION_TOOL_DESCRIPTION,
        "tool": function_tool,
        "schema": function_tool.schema,
        "reflect_tool_result": True,
    }
