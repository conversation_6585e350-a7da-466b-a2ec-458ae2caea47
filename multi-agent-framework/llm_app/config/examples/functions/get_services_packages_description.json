{"function_id": "get_services_packages_description", "code": "def get_services_packages_description(\n    Salons_Location_Name: Annotated[str, \"Represents the valid salon location name selected by customer.\"],\n    service_package_name_list: Annotated[list,\"A list of valid service or package names for which descriptions need to be retrieved.\",],\n    context_arguments: dict = {},\n) -> str:\n    \"\"\"\n    Retrieves the service descriptions for specific services or packages at a given salon location.\n    This function should be used after the customer has selected a valid salon location and chosen specific service or package names. \n    The provided service or package names must be valid and obtained from the result of the `get_staff_service_map` function.\n\n    Args:\n        Salons_Location_Name (str, required): Represents the valid salon location name selected by customer.\n        service_package_name_list (list, required): A list of valid services or packages names whose descriptions need to be retrieved.\n        context_arguments (dict, optional): Framework context data. Defaults to {}.\n\n    Returns:\n        str: Returns a JSON string with service/package descriptions for the specified salon and service names.\n    \"\"\"\n    body = {\n        \"business_id\": \"Wj6YQfGEsvSqkLskUdhu3g\",\n        \"chat_id\": context_arguments.get(\"chat_id\"),\n        \"Salons_Location_Name\": Salons_Location_Name,\n        \"service_package_name_list\": service_package_name_list,\n    }\n    url = \"http://brook.dev-scc-demo.rozie.ai/phorest/get_services_packages_description\"\n    headers = {\"Content-Type\": \"application/json\"}\n    param = {\n        \"url\": url,\n        \"timeout\": 30,\n        \"headers\": headers,\n    }\n    if body:\n        param[\"data\"] = json.dumps(body)\n\n    try:\n        response = requests.post(**param)\n        if response.status_code not in [200, 201, 202]:\n            if response.json():\n                return \"Failed\"\n            return \"Failed\"\n        response_data = response.json()\n    except Exception as e:\n        print(\"Error in get_service_package_details:\", e)\n        return f\"Failed {str(e)}\"\n\n    return json.dumps(response_data, indent=2)", "description": "This function returns descriptions of specific services or packages available at a given salon location. It requires a valid salon location name and a list of service or package names that the customer has selected. These service or package names must be obtained from the output of the `get_staff_service_map` function to ensure they are valid. The function responds with a structured JSON containing information about service description.", "function_name": "get_services_packages_description", "roster_id": ""}