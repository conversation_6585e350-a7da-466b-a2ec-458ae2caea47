from uuid import uuid4
from loguru import logger
from typing import List, Dict, Any

from llm_app.pipeline.models.data import PipelineData
from llm_app.pipeline.steps.response_builder.agent.models.SendMessageResponse import AgentResponse
from llm_app.pipeline.steps.response_builder.helpers.template_helper import render_template
from llm_app.pipeline.models.state import PipelineState

# ----------------- Shared Utility -----------------


def wrap_response(
    parsed_responses: List[Dict], event_data: Dict, state: PipelineState, override_next_action: str = None
) -> Dict:
    """
    Wrap parsed responses into the final message response format.
    """
    result = {
        "version": event_data.get("version", "1.0"),
        "rozie_user_id": {},
        "user_info": event_data.get("user_info"),
        "response_map": {"responses": {"default": parsed_responses}},
        "should_end_interaction": True if state.should_end_interaction else False,
        "next_action": "say" if state.partial_response else "gather",
    }
    if override_next_action:
        result["next_action"] = override_next_action
    return result


def format_template_response(template_name: str, template_values: Dict[str, Any]) -> List[Dict]:
    """
    Format a template response based on the template name and values.
    """
    parsed_responses = []

    try:
        html_content, css_content = render_template(template_name, template_values)

        if html_content:
            parsed_responses.append(
                {
                    "response_template": {
                        "response_type": "html",
                        "cardName": template_name,
                        "body": html_content,
                        "css": css_content,
                    }
                }
            )

            if template_values.get("summary_text"):
                parsed_responses.insert(
                    0,
                    {
                        "response_template": {
                            "response_type": "text",
                            "text": template_values["summary_text"],
                        }
                    },
                )
        else:
            logger.warning(f"Template {template_name} rendered empty content")
            parsed_responses.append(
                {
                    "response_template": {
                        "response_type": "text",
                        "text": f"I found information about {template_name}, but I'm having trouble displaying it in a formatted way.",
                    }
                }
            )

    except Exception as e:
        logger.error(f"Error rendering template {template_name}: {str(e)}")
        parsed_responses.append(
            {
                "response_template": {
                    "response_type": "text",
                    "text": f"I found information related to your request, but I'm having trouble displaying it in a formatted way.",
                }
            }
        )

    return parsed_responses


def format_template_response_message(template_name: str, template_values: Dict[str, Any], data: PipelineData) -> Dict:
    """
    Create a complete response message for a template response.
    """
    parsed_responses = format_template_response(template_name, template_values)
    return wrap_response(parsed_responses, data.event.raw_event, data.state)


# ----------------- LLM Agent Helpers -----------------


def format_llm_agent_response(llm_agent_response: str, response_builder_response: AgentResponse) -> List[Dict]:
    """
    Format the response based on the agent's message type and content.
    """
    parsed_responses = []

    if response_builder_response.send_message.type == "text":
        parsed_responses.append({"response_template": {"response_type": "text", "text": llm_agent_response}})

    elif response_builder_response.send_message.type == "options":
        options = [
            {"id": f"context_key_{uuid4()}", "label": option, "priority": 1}
            for option in response_builder_response.send_message.options
        ]

        parsed_responses.append(
            {"response_template": {"response_type": "text", "text": response_builder_response.send_message.message}}
        )

        parsed_responses.append({"response_template": {"response_type": "quick_reply", "items": options}})

    else:
        logger.warning(
            "Unsupported response type from response_builder: {}", response_builder_response.send_message.type
        )

    return parsed_responses


def format_llm_response_message(
    llm_agent_response: str,
    response_builder_response: AgentResponse,
    data: PipelineData,
    override_next_action: str = None,
) -> Dict:
    """
    Generate the complete final response message based on LLM output and response builder metadata.
    """
    logger.debug(
        "Formatting final response",
        data={"llm_agent_response": llm_agent_response, "response_builder_response": response_builder_response},
    )

    parsed_responses = format_llm_agent_response(llm_agent_response, response_builder_response)
    return wrap_response(parsed_responses, data.event.raw_event, data.state, override_next_action)


def format_knowledge_base_response_message(
    references: List[Dict], knowledge_base_query: str, data: PipelineData
) -> Dict:
    """
    Generate the complete final response message based on LLM output and response builder metadata.
    """
    logger.debug(
        "Formatting final response",
        data={"references": references, "knowledge_base_query": knowledge_base_query},
    )

    return wrap_response(
        [{"response_template": {"response_type": "search", "documents": references, "metadata": {}}}], data.event.raw_event, data.state
    )
