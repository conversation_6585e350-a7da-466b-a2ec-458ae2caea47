# This file was auto-generated by Fern from our API Definition.

from __future__ import annotations

import datetime as dt
import typing

from ....core.datetime_utils import serialize_datetime
from ....core.pydantic_utilities import deep_union_pydantic_dicts, pydantic_v1
from .score_source import ScoreSource


class Score_Numeric(pydantic_v1.BaseModel):
    value: float
    id: str
    trace_id: str = pydantic_v1.Field(alias="traceId")
    name: str
    source: ScoreSource
    observation_id: typing.Optional[str] = pydantic_v1.Field(
        alias="observationId", default=None
    )
    timestamp: dt.datetime
    created_at: dt.datetime = pydantic_v1.Field(alias="createdAt")
    updated_at: dt.datetime = pydantic_v1.Field(alias="updatedAt")
    author_user_id: typing.Optional[str] = pydantic_v1.Field(
        alias="authorUserId", default=None
    )
    comment: typing.Optional[str] = None
    config_id: typing.Optional[str] = pydantic_v1.Field(alias="configId", default=None)
    queue_id: typing.Optional[str] = pydantic_v1.Field(alias="queueId", default=None)
    data_type: typing.Literal["NUMERIC"] = pydantic_v1.Field(
        alias="dataType", default="NUMERIC"
    )

    def json(self, **kwargs: typing.Any) -> str:
        kwargs_with_defaults: typing.Any = {
            "by_alias": True,
            "exclude_unset": True,
            **kwargs,
        }
        return super().json(**kwargs_with_defaults)

    def dict(self, **kwargs: typing.Any) -> typing.Dict[str, typing.Any]:
        kwargs_with_defaults_exclude_unset: typing.Any = {
            "by_alias": True,
            "exclude_unset": True,
            **kwargs,
        }
        kwargs_with_defaults_exclude_none: typing.Any = {
            "by_alias": True,
            "exclude_none": True,
            **kwargs,
        }

        return deep_union_pydantic_dicts(
            super().dict(**kwargs_with_defaults_exclude_unset),
            super().dict(**kwargs_with_defaults_exclude_none),
        )

    class Config:
        frozen = True
        smart_union = True
        allow_population_by_field_name = True
        populate_by_name = True
        extra = pydantic_v1.Extra.allow
        json_encoders = {dt.datetime: serialize_datetime}


class Score_Categorical(pydantic_v1.BaseModel):
    value: typing.Optional[float] = None
    string_value: str = pydantic_v1.Field(alias="stringValue")
    id: str
    trace_id: str = pydantic_v1.Field(alias="traceId")
    name: str
    source: ScoreSource
    observation_id: typing.Optional[str] = pydantic_v1.Field(
        alias="observationId", default=None
    )
    timestamp: dt.datetime
    created_at: dt.datetime = pydantic_v1.Field(alias="createdAt")
    updated_at: dt.datetime = pydantic_v1.Field(alias="updatedAt")
    author_user_id: typing.Optional[str] = pydantic_v1.Field(
        alias="authorUserId", default=None
    )
    comment: typing.Optional[str] = None
    config_id: typing.Optional[str] = pydantic_v1.Field(alias="configId", default=None)
    queue_id: typing.Optional[str] = pydantic_v1.Field(alias="queueId", default=None)
    data_type: typing.Literal["CATEGORICAL"] = pydantic_v1.Field(
        alias="dataType", default="CATEGORICAL"
    )

    def json(self, **kwargs: typing.Any) -> str:
        kwargs_with_defaults: typing.Any = {
            "by_alias": True,
            "exclude_unset": True,
            **kwargs,
        }
        return super().json(**kwargs_with_defaults)

    def dict(self, **kwargs: typing.Any) -> typing.Dict[str, typing.Any]:
        kwargs_with_defaults_exclude_unset: typing.Any = {
            "by_alias": True,
            "exclude_unset": True,
            **kwargs,
        }
        kwargs_with_defaults_exclude_none: typing.Any = {
            "by_alias": True,
            "exclude_none": True,
            **kwargs,
        }

        return deep_union_pydantic_dicts(
            super().dict(**kwargs_with_defaults_exclude_unset),
            super().dict(**kwargs_with_defaults_exclude_none),
        )

    class Config:
        frozen = True
        smart_union = True
        allow_population_by_field_name = True
        populate_by_name = True
        extra = pydantic_v1.Extra.allow
        json_encoders = {dt.datetime: serialize_datetime}


class Score_Boolean(pydantic_v1.BaseModel):
    value: float
    string_value: str = pydantic_v1.Field(alias="stringValue")
    id: str
    trace_id: str = pydantic_v1.Field(alias="traceId")
    name: str
    source: ScoreSource
    observation_id: typing.Optional[str] = pydantic_v1.Field(
        alias="observationId", default=None
    )
    timestamp: dt.datetime
    created_at: dt.datetime = pydantic_v1.Field(alias="createdAt")
    updated_at: dt.datetime = pydantic_v1.Field(alias="updatedAt")
    author_user_id: typing.Optional[str] = pydantic_v1.Field(
        alias="authorUserId", default=None
    )
    comment: typing.Optional[str] = None
    config_id: typing.Optional[str] = pydantic_v1.Field(alias="configId", default=None)
    queue_id: typing.Optional[str] = pydantic_v1.Field(alias="queueId", default=None)
    data_type: typing.Literal["BOOLEAN"] = pydantic_v1.Field(
        alias="dataType", default="BOOLEAN"
    )

    def json(self, **kwargs: typing.Any) -> str:
        kwargs_with_defaults: typing.Any = {
            "by_alias": True,
            "exclude_unset": True,
            **kwargs,
        }
        return super().json(**kwargs_with_defaults)

    def dict(self, **kwargs: typing.Any) -> typing.Dict[str, typing.Any]:
        kwargs_with_defaults_exclude_unset: typing.Any = {
            "by_alias": True,
            "exclude_unset": True,
            **kwargs,
        }
        kwargs_with_defaults_exclude_none: typing.Any = {
            "by_alias": True,
            "exclude_none": True,
            **kwargs,
        }

        return deep_union_pydantic_dicts(
            super().dict(**kwargs_with_defaults_exclude_unset),
            super().dict(**kwargs_with_defaults_exclude_none),
        )

    class Config:
        frozen = True
        smart_union = True
        allow_population_by_field_name = True
        populate_by_name = True
        extra = pydantic_v1.Extra.allow
        json_encoders = {dt.datetime: serialize_datetime}


Score = typing.Union[Score_Numeric, Score_Categorical, Score_Boolean]
