{"function_id": "check_representative_support", "code": "def check_representative_support(\n    branch_name: Annotated[str, \"Name of the branch for which availability of agents is to be checked\"],\n    context_arguments: dict = {},\n) -> dict:\n    \"\"\"\n    This function check the availability of agents to transfer the call.\n    If available it will initiate the transfer. \n    \"\"\"\n    params = {\n        \"chat_id\": context_arguments.get(\"chat_id\"),\n        \"business_id\": \"Wj6YQfGEsvSqkLskUdhu3g\",\n        \"branch_name\": branch_name,\n        \"self_service_flag\": False,\n    }\n    \n    url = \"https://brook.dev-scc-demo.rozie.ai/transfer/escalate_call\"\n    headers = {\"Content-Type\": \"application/json\"}\n\n    try:\n        response = requests.get(\n            url,\n            params=params,\n            headers=headers,\n        )\n        if response.status_code != 200:\n            return \"Failed\"\n        response_data = response.json()\n        return response_data\n    except Exception as e:\n        return \"Failed\", str(e)", "description": "Function will check the availability of the representative, if availability call will be transferred to representative.", "function_name": "check_representative_support", "roster_id": "", "reflect_tool_result": false}