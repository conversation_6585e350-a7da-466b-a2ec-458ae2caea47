{"function_id": "rozieair_get_flight_offers_search", "code": "def rozieair_get_flight_offers_search(\n    origin: Annotated[str, \"Origin airport code (e.g., PAR)\"],\n    destination: Annotated[str, \"Destination airport code (e.g., ICN)\"],\n    departure_date: Annotated[str, \"Departure date (YYYY-MM-DD)\"],\n    return_date: Annotated[str, \"Return date for round trips (YYYY-MM-DD)\"] = None,\n    adults: Annotated[int, \"Number of adult passengers\"] = 1,\n    children: Annotated[int, \"Number of child passengers\"] = 0,\n    infants: Annotated[int, \"Number of infant passengers\"] = 0,\n    context_arguments: dict = {}\n) -> dict:\n    \"\"\"Search for flight offers using the flight offers search API.\"\"\"\n    query_params = {\n        \"origin\": origin.upper(),\n        \"destination\": destination.upper(),\n        \"departure_date\": departure_date,\n        \"adults\": adults,\n        \"children\": children,\n        \"infants\": infants,\n        \"max_offers\": 3\n    }\n\n    if return_date:\n        query_params[\"return_date\"] = return_date\n\n    url = \"https://rozie-amadeus.dev-scc-demo.rozie.ai/api/v1/flight-offers-search\"\n    headers = {\"accept\": \"application/json\"}\n\n    try:\n        response = requests.get(url, headers=headers, params=query_params, timeout=30)\n        if response.status_code != 200:\n            return {\n                \"llm_result\": {\"error\": f\"Flight offer API error: {response.status_code}\"},\n                \"custom_results\": []\n            }\n\n        result = response.json()\n\n        # Process flight offers for template\n        processed_offers = []\n        for idx, offer in enumerate(result.get(\"data\", []), 1):\n            price = offer.get(\"price\", {}).get(\"total\", \"0.00\")\n            currency = offer.get(\"price\", {}).get(\"currency\", \"EUR\")\n            itineraries = offer.get(\"itineraries\", [])\n\n            if not itineraries:\n                continue\n\n            outbound = itineraries[0]\n            return_trip = itineraries[1] if len(itineraries) > 1 else None\n\n            # Process outbound segments\n            outbound_segments = []\n            for segment in outbound.get(\"segments\", []):\n                departure = segment.get(\"departure\", {})\n                arrival = segment.get(\"arrival\", {})\n\n                # Format datetime strings\n                try:\n                    departure_time = datetime.fromisoformat(departure.get(\"at\", \"\")).strftime(\"%b %d, %Y – %H:%M\")\n                except Exception:\n                    departure_time = departure.get(\"at\", \"\") or \"N/A\"\n\n                try:\n                    arrival_time = datetime.fromisoformat(arrival.get(\"at\", \"\")).strftime(\"%b %d, %Y – %H:%M\")\n                except Exception:\n                    arrival_time = arrival.get(\"at\", \"\") or \"N/A\"\n\n                flight_number = f\"{segment.get('carrierCode', '')} {segment.get('number', '')}\".strip()\n                aircraft = segment.get(\"aircraft\", {}).get(\"code\", \"\")\n\n                outbound_segments.append({\n                    \"departure_code\": departure.get(\"iataCode\", \"\"),\n                    \"departure_time\": departure_time,\n                    \"arrival_code\": arrival.get(\"iataCode\", \"\"),\n                    \"arrival_time\": arrival_time,\n                    \"flight_number\": flight_number,\n                    \"aircraft\": aircraft\n                })\n\n            # Process return segments if exists\n            return_segments = []\n            if return_trip:\n                for segment in return_trip.get(\"segments\", []):\n                    departure = segment.get(\"departure\", {})\n                    arrival = segment.get(\"arrival\", {})\n\n                    try:\n                        departure_time = datetime.fromisoformat(departure.get(\"at\", \"\")).strftime(\"%b %d, %Y – %H:%M\")\n                    except Exception:\n                        departure_time = departure.get(\"at\", \"\") or \"N/A\"\n\n                    try:\n                        arrival_time = datetime.fromisoformat(arrival.get(\"at\", \"\")).strftime(\"%b %d, %Y – %H:%M\")\n                    except Exception:\n                        arrival_time = arrival.get(\"at\", \"\") or \"N/A\"\n\n                    flight_number = f\"{segment.get('carrierCode', '')} {segment.get('number', '')}\".strip()\n                    aircraft = segment.get(\"aircraft\", {}).get(\"code\", \"\")\n\n                    return_segments.append({\n                        \"departure_code\": departure.get(\"iataCode\", \"\"),\n                        \"departure_time\": departure_time,\n                        \"arrival_code\": arrival.get(\"iataCode\", \"\"),\n                        \"arrival_time\": arrival_time,\n                        \"flight_number\": flight_number,\n                        \"aircraft\": aircraft\n                    })\n\n            # Calculate summary info\n            origin_code = outbound[\"segments\"][0].get(\"departure\", {}).get(\"iataCode\", \"\")\n            dest_code = outbound[\"segments\"][-1].get(\"arrival\", {}).get(\"iataCode\", \"\")\n            outbound_duration = outbound.get(\"duration\", \"PT0H0M\").replace(\"PT\", \"\").replace(\"H\", \"h \").replace(\"M\", \"m\")\n            return_duration = return_trip.get(\"duration\", \"PT0H0M\").replace(\"PT\", \"\").replace(\"H\", \"h \").replace(\"M\", \"m\") if return_trip else \"\"\n            stops_count = max(len(outbound.get(\"segments\", [])) - 1, 0)\n            stops_str = f\"{stops_count} Stop\" + (\"\" if stops_count == 1 else \"s\")\n\n            processed_offers.append({\n                \"index\": idx,\n                \"offer_id\": offer.get(\"id\", \"\"),\n                \"price\": price,\n                \"currency\": currency,\n                \"origin_code\": origin_code,\n                \"dest_code\": dest_code,\n                \"outbound_duration\": outbound_duration,\n                \"return_duration\": return_duration,\n                \"stops_str\": stops_str,\n                \"outbound_segments\": outbound_segments,\n                \"return_segments\": return_segments,\n                \"has_return\": bool(return_trip),\n                \"is_first\": idx == 1\n            })\n\n        return {\n            \"llm_result\": result,\n            \"offers\": processed_offers,\n            \"search_params\": {\n                \"origin\": origin.upper(),\n                \"destination\": destination.upper(),\n                \"departure_date\": departure_date,\n                \"return_date\": return_date,\n                \"adults\": adults,\n                \"children\": children,\n                \"infants\": infants\n            }\n        }\n        \n    except Exception as e:\n        return {\n            \"llm_result\": {\"error\": str(e)},\n            \"custom_results\": []\n        }", "description": "Search for flight offers and return a formatted card with flight details. The function returns processed flight data that will be formatted using the flight-offers template.", "function_name": "rozieair_get_flight_offers_search", "reflect_tool_result": false, "template": "flight-offers"}