{# Flight Offers Template #}
<div class="container">
  {% for offer in offers %}
  <details{% if offer.is_first %} open{% endif %}>
    <summary class="accordion">
      <div class="label">#{{ offer.index }}</div>
      <div class="summary-info">
        <span><strong>{{ offer.origin_code }} → {{ offer.dest_code }}</strong> • {{ offer.outbound_duration }}</span>
        {% if offer.has_return %}
        <span>Return: {{ offer.return_duration }}</span>
        {% endif %}
        <span>{{ offer.stops_str }}</span>
      </div>
      <div class="price">{{ offer.currency }} {{ offer.price }}</div>
      <div class="arrow">{% if offer.is_first %}▲{% else %}▼{% endif %}</div>
    </summary>
    
    <div class="panel">
      <div class="panel-content">
        
        {# Outbound segments #}
        <div class="segment-wrapper">
          <div class="segment-container">
            {% for segment in offer.outbound_segments %}
            <div class="timeline-item">
              <div class="timeline-circle"></div>
              <div class="timeline-location">{{ segment.departure_code }}</div>
              <div class="timeline-time">{{ segment.departure_time }}</div>
            </div>
            <div class="timeline-connector">
              <div class="timeline-dots"></div>
            </div>
            <div class="timeline-item">
              <div class="timeline-circle"></div>
              <div class="timeline-location">{{ segment.arrival_code }}</div>
              <div class="timeline-time">{{ segment.arrival_time }}</div>
            </div>
            <div class="segment-detail">
              <p>Flight {{ segment.flight_number }} • {{ segment.aircraft }}</p>
            </div>
            {% endfor %}
          </div>
        </div>

        {# Return segments if exists #}
        {% if offer.has_return %}
        <div class="separator">
          <div class="dotted-line"></div>
          <div class="return-label">Return Flight</div>
          <div class="dotted-line"></div>
        </div>

        <div class="segment-wrapper">
          <div class="segment-container">
            {% for segment in offer.return_segments %}
            <div class="timeline-item">
              <div class="timeline-circle"></div>
              <div class="timeline-location">{{ segment.departure_code }}</div>
              <div class="timeline-time">{{ segment.departure_time }}</div>
            </div>
            <div class="timeline-connector">
              <div class="timeline-dots"></div>
            </div>
            <div class="timeline-item">
              <div class="timeline-circle"></div>
              <div class="timeline-location">{{ segment.arrival_code }}</div>
              <div class="timeline-time">{{ segment.arrival_time }}</div>
            </div>
            <div class="segment-detail">
              <p>Flight {{ segment.flight_number }} • {{ segment.aircraft }}</p>
            </div>
            {% endfor %}
          </div>
        </div>
        {% endif %}

        {# Select button #}
        <div class="select-button-wrapper">
          <button type="button" onclick="
            const msg = 'Flight selected {{ offer.offer_id }}';
            const shell = document.querySelector('rozieai-webchat')?.shadowRoot
              ?.querySelector('webchat-shell')?.shadowRoot;
            const input = shell?.querySelector('textarea.user-input');
            const button = shell?.querySelector('button.submit-btn');
            if (!input || !button) {
              alert('Simulated message: ' + msg);
              return console.warn('RozieAI input or button not found. Running in test mode.');
            }
            input.value = msg;
            input.dispatchEvent(new Event('input', { bubbles: true }));
            setTimeout(() => button.click(), 200);
          ">Select Flight</button>
        </div>
        
      </div>
    </div>
  </details>
  {% endfor %}
</div>

<style>
body {
  margin: 0;
  background-color: #f9f9f9;
  font-family: 'Segoe UI', sans-serif;
  font-size: 12px;
  color: #333;
}
.container {
  max-width: 100%;
  padding: 8px;
  box-sizing: border-box;
}
summary.accordion {
  background: linear-gradient(45deg, rgb(83, 74, 216) -23.5%, rgb(131, 76, 157) 107.38%);
  color: white;
  cursor: pointer;
  padding: 10px 12px;
  border-radius: 6px;
  font-size: 12px;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6px;
}
summary::-webkit-details-marker {
  display: none;
}
summary::marker {
  content: "";
}
details[open] summary.accordion {
  background: linear-gradient(45deg, rgb(83, 74, 216) -23.5%, rgb(131, 76, 157) 107.38%);
}
.label {
  font-weight: bold;
  background-color: #fff;
  color: #834C9D;
  border-radius: 4px;
  padding: 1px 5px;
  font-size: 11px;
  margin-right: 8px;
}
.summary-info {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  flex: 1;
}
.summary-info span {
  font-size: 11px;
  white-space: nowrap;
}
.price {
  font-size: 12px;
  color: #b8ffcc;
  font-weight: bold;
  margin-left: auto;
}
.arrow {
  font-size: 10px;
  margin-left: 6px;
  transform: rotate(0deg);
  transition: transform 0.2s ease;
}
details[open] .arrow {
  transform: rotate(180deg);
}
.panel {
  background: #f0f0f8;
  border: 1px solid #ccc;
  border-top: none;
  border-radius: 0 0 6px 6px;
  padding: 10px;
  margin-top: -4px;
}
.panel-content {
  display: flex;
  flex-direction: column;
}
.segment-wrapper {
  margin-bottom: 16px;
}
.segment-container {
  flex: 1;
}
.separator {
  display: flex;
  align-items: center;
  margin: 12px 0;
}
.dotted-line {
  flex-grow: 1;
  border-bottom: 1px dotted #aaa;
}
.return-label {
  padding: 0 8px;
  font-size: 11px;
  color: #555;
  font-weight: bold;
}
.select-button-wrapper {
  text-align: center;
  margin-top: 20px;
}
.select-button-wrapper button {
  background: #534ad8;
  color: white;
  padding: 6px 12px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
}
.timeline-item {
  display: flex;
  align-items: center;
  margin: 6px 0;
  gap: 6px;
}
.timeline-circle {
  width: 10px;
  height: 10px;
  border: 2px solid #777;
  border-radius: 50%;
  background: white;
}
.timeline-connector {
  margin-left: 3px;
}
.timeline-dots {
  border-left: 2px dotted #777;
  height: 24px;
  margin: 4px 0;
}
.timeline-time, .timeline-location {
  font-size: 13px;
  color: #333;
  font-weight: 500;
}
.segment-detail {
  font-size: 11px;
  margin-bottom: 8px;
}
</style>
