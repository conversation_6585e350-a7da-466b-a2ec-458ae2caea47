{"function_id": "get_finishes_details", "code": "def get_finishes_details(\n    branch_name: Annotated[str, \"The name of the salon branch where services will be performed. This is a required field.\"],\n    service_list: Annotated[list, \"A list of services explicitly selected by the customer to check for finishes. At least one service is required.\"],\n    context_arguments: dict = {}\n) -> str:\n    \"\"\"\n    Determine if selected services require finishes and return available finish options.\n    \n    Args:\n        branch_name: The salon branch where services will be performed\n        service_list: List of service names to check for finishes requirements\n        context_arguments: Additional context parameters including chat_id\n        \n    Returns:\n        str: JSON string containing information about finish requirements\n            - If finishes are not required, returns status and suggested action\n            - If finishes are required, returns status, suggested action, available finish options, \n              and services that need finishes\n    \"\"\"\n    body = {\n        \"business_id\": \"Wj6YQfGEsvSqkLskUdhu3g\",\n        \"branch_name\": branch_name,\n        \"chat_id\": context_arguments.get(\"chat_id\", \"\"),\n        \"service_list\": service_list\n    }\n\n    url = \"https://brook.dev-scc-demo.rozie.ai/phorest/get_finishes_details\"\n    headers = {\"Content-Type\": \"application/json\"}\n    param = {\n        \"url\": url,\n        \"timeout\": 30,\n        \"headers\": headers,\n    }\n\n    if body:\n        param[\"data\"] = json.dumps(body)\n        try:\n            response = requests.post(**param)\n            if response.status_code != 200:\n                return \"Failed\"\n            response_json = response.json()\n            return json.dumps(response_json, indent=2)\n        except Exception as e:\n            return \"Failed\", str(e)\n", "description": "The get_finishes_details function helps determine if selected salon services require finishing services,and provides available finish options when needed. To use it, provide the branch_name (the salon location where services will be performed) and service_list (an array of service names you want to book). The function analyzes these services and determines if any finishes are required.", "function_name": "get_finishes_details", "roster_id": ""}