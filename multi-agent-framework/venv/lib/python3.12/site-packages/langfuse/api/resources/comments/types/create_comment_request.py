# This file was auto-generated by Fern from our API Definition.

import datetime as dt
import typing

from ....core.datetime_utils import serialize_datetime
from ....core.pydantic_utilities import deep_union_pydantic_dicts, pydantic_v1


class CreateCommentRequest(pydantic_v1.BaseModel):
    project_id: str = pydantic_v1.Field(alias="projectId")
    """
    The id of the project to attach the comment to.
    """

    object_type: str = pydantic_v1.Field(alias="objectType")
    """
    The type of the object to attach the comment to (trace, observation, session, prompt).
    """

    object_id: str = pydantic_v1.Field(alias="objectId")
    """
    The id of the object to attach the comment to. If this does not reference a valid existing object, an error will be thrown.
    """

    content: str = pydantic_v1.Field()
    """
    The content of the comment. May include markdown. Currently limited to 500 characters.
    """

    author_user_id: typing.Optional[str] = pydantic_v1.Field(
        alias="authorUserId", default=None
    )
    """
    The id of the user who created the comment.
    """

    def json(self, **kwargs: typing.Any) -> str:
        kwargs_with_defaults: typing.Any = {
            "by_alias": True,
            "exclude_unset": True,
            **kwargs,
        }
        return super().json(**kwargs_with_defaults)

    def dict(self, **kwargs: typing.Any) -> typing.Dict[str, typing.Any]:
        kwargs_with_defaults_exclude_unset: typing.Any = {
            "by_alias": True,
            "exclude_unset": True,
            **kwargs,
        }
        kwargs_with_defaults_exclude_none: typing.Any = {
            "by_alias": True,
            "exclude_none": True,
            **kwargs,
        }

        return deep_union_pydantic_dicts(
            super().dict(**kwargs_with_defaults_exclude_unset),
            super().dict(**kwargs_with_defaults_exclude_none),
        )

    class Config:
        frozen = True
        smart_union = True
        allow_population_by_field_name = True
        populate_by_name = True
        extra = pydantic_v1.Extra.allow
        json_encoders = {dt.datetime: serialize_datetime}
