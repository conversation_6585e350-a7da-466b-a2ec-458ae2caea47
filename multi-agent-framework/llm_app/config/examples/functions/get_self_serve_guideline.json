{"function_id": "get_self_serve_guideline", "code": "def get_self_serve_guideline(\n    context_arguments: dict = {},\n) -> dict:\n    \"\"\"\n    This function will return the self serve instructions.\n    \"\"\"\n    self_serve_guideline = \"\"\"\nIf the customer's reason for wanting to speak with a live agent is clearly present in the conversation_context:\n    Do not ask for the reason again.\n    Proceed directly to the callback route.\n    \nIf the customer's reason is not clear in the conversation_context:\n    Prompt the customer to briefly explain why they want to connect with a live agent.\n    Must wait for their response before taking further action.\n\n    After receiving the customer's response:\n        If their request is related to New Booking, Rescheduling, Cancellation, Appointment Inquiry:\n            Inform the customer that you can assist with that directly.\n            Then, ask the customer to confirm if they would like to proceed with your assistance.\n\"\"\"\n    return self_serve_guideline\n", "description": "This function will return the self serve instructions.", "function_name": "get_self_serve_guideline", "roster_id": ""}