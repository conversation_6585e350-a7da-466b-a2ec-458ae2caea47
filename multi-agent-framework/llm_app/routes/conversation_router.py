import asyncio
import json
import uuid
from fastapi import (
    APIRouter,
    Request,
    WebSocket,
    WebSocketDisconnect,
    Depends,
    BackgroundTasks,
    HTTPException,
)

from collections.abc import Callable, Awaitable


from typing import Any
from loguru import logger

# Pipeline components
from llm_app.pipeline.models.data import PipelineData
from llm_app.pipeline.steps.preprocessor.request_normalizer import RequestNormalizer
from llm_app.pipeline.steps.response_builder.response_builder import ResponseBuilder
from llm_app.pipeline.steps.agentic_framework.autogen.processor import AutoGenProcessor

from llm_app.adapters.auth_adapter import get_api_key, ws_auth
from llm_app.models.endpoint_response import APIRequest, api_client_event_parser
from llm_app.logging.logging_context import (
    conversation_id,
    channel_user_id_var,
    user_id_var,
)
from llm_app.analytics.registry import get_analytics_service
from llm_app.analytics.event_name import EventName
from llm_app.helpers.task_manager import task_manager

router = APIRouter()

# Create shared processor instance
processor = AutoGenProcessor({})

# Inject processor into steps
normalizer = RequestNormalizer(processor)
response_builder = ResponseBuilder()
chat_id_mapping = {}
task_id_mapping = {}


def set_context_variables(chat_id, user_id, channel_user_id):
    conversation_id.set(chat_id)
    user_id_var.set(user_id)
    channel_user_id_var.set(channel_user_id)


async def analytics_event(
    analytics_service,
    chatId: str,
    event_type: EventName,
    header,
    request: dict = {},
    response: dict = {},
):
    if event_type == EventName.NEW_USER_CREATED:
        await analytics_service.add_new_user_created_event(chatId, request, header)
    if event_type == EventName.CONVERSATION_STARTED:
        await analytics_service.add_conversation_started_event(chatId, request, header)
    elif event_type == EventName.REQUEST_RECEIVED:
        await analytics_service.add_request_received_event(chatId, request, header)
    elif event_type == EventName.RESPONSE_SENT:
        await analytics_service.add_response_sent_event(chatId, response, header)
    elif event_type == EventName.CONVERSATION_ENDED:
        await analytics_service.add_conversation_ended_event(chatId, response, header)


async def build_and_run_pipeline_streaming(
    initial_payload: dict,
    send_fn: Callable[[dict], Awaitable[None]],
):
    try:
        data = PipelineData.from_payload(initial_payload)

        # Step 1: Normalize
        data = await normalizer.process(data)

        # Step 2: Run processor and stream all intermediate data
        async for partial_data in processor.process(data):
            # Step 3: Run response builder on each partial processor result
            async for response_data in response_builder.process(partial_data):
                # Step 4: Serialize and send each response state
                await send_fn(response_data.state.channel_response)

    except Exception as e:
        import traceback

        traceback.print_exc()
        raise e


async def build_and_run_pipeline_shutdown() -> Any:
    try:
        # TODO add pipeline to clean the resources
        pass

    except Exception as e:
        import traceback

        traceback.print_exc()
        return "error"


@router.websocket("/conversation/ws/{chat_id}", dependencies=[Depends(ws_auth)])
async def conversation_ws(
    websocket: WebSocket,
    chat_id: str,
    analytics_service=Depends(get_analytics_service),
):
    await websocket.accept()
    is_processing = False
    chat_id = f"conversation_{uuid.uuid4()}"

    try:
        while True:
            raw_data = await websocket.receive_text()

            request_body = json.loads(raw_data)
            unique_id = request_body.get("user_info").get("user_id").get("id")
            set_context_variables(chat_id, unique_id, unique_id)
            print(f"request_body: {request_body}")
            for incoming_event in request_body.get("incoming_events", []):
                print(f"incoming_event: {incoming_event}")
                if incoming_event.get("event_template").get("event_type") == "initiate":
                    print("initiated", chat_id,
                        EventName.NEW_USER_CREATED,
                        websocket.headers,
                        request_body)
                    asyncio.create_task(
                        analytics_event(
                            analytics_service,
                            chat_id,
                            EventName.NEW_USER_CREATED,
                            websocket.headers,
                            request_body,
                            {},
                        )
                    )
                    asyncio.create_task(
                        analytics_event(
                            analytics_service,
                            chat_id,
                            EventName.CONVERSATION_STARTED,
                            websocket.headers,
                            request_body,
                            {},
                        )
                    )
                if incoming_event.get("event_template").get("event_type") in [
                    "text",
                    "contextkey",
                ]:
                    asyncio.create_task(
                        analytics_event(
                            analytics_service,
                            chat_id,
                            EventName.REQUEST_RECEIVED,
                            websocket.headers,
                            request_body,
                            {},
                        )
                    )
            if is_processing:
                await websocket.send_text(json.dumps({"type": "busy"}))
                continue

            is_processing = True
            try:
                client_event: APIRequest = api_client_event_parser(raw_data)
            except Exception as e:
                await websocket.send_text(json.dumps({"type": "error", "message": f"Invalid format: {str(e)}"}))
                is_processing = False
                continue

            async def send_fn(payload: dict):
                if payload["should_end_interaction"] or payload["next_action"] == "disconnect":
                    asyncio.create_task(
                        analytics_event(
                            analytics_service,
                            chat_id,
                            EventName.CONVERSATION_ENDED,
                            websocket.headers,
                            {},
                            payload
                        )
                    )
                else:
                    asyncio.create_task(
                        analytics_event(
                            analytics_service,
                            chat_id,
                            EventName.RESPONSE_SENT,
                            websocket.headers,
                            {},
                            payload
                        )
                    )
                await websocket.send_text(json.dumps(payload))

            try:
                await build_and_run_pipeline_streaming(client_event.model_dump(), send_fn)
                is_processing = False
            except Exception as e:
                await websocket.send_text(json.dumps({"type": "error", "message": f"Error {str(e)}"}))
                is_processing = False
                continue

    except WebSocketDisconnect as exception:
        asyncio.create_task(
            analytics_event(
                analytics_service,
                chat_id,
                EventName.CONVERSATION_ENDED,
                websocket.headers,
                {},
                {}
            )
        )
        logger.exception(f"WebSocket disconnected for chat_id: {chat_id} with exception: {exception}")
    finally:
        pass


async def send_result_to_task(payload: dict):
    unique_id = payload.get("user_info").get("user_id").get("id")
    chat_id = chat_id_mapping.get(unique_id)
    task_id = task_id_mapping.get(chat_id)
    task_manager.add_result(task_id, json.dumps(payload))


@router.post("/conversation/initiate_chat", dependencies=[Depends(get_api_key)])
async def initiate_chat(
    request_body: dict,
    req: Request,
    background_tasks: BackgroundTasks,
    analytics_service=Depends(get_analytics_service),
) -> dict:
    try:
        chat_id = f"conversation_{uuid.uuid4()}"
        unique_id = request_body.get("user_info").get("user_id").get("id")
        set_context_variables(chat_id, unique_id, unique_id)
        for incoming_event in request_body.get("incoming_events"):
            if incoming_event.get("event_template", {}).get("event_type") == "initiate":
                task_id = task_manager.start_task()
                asyncio.create_task(build_and_run_pipeline_streaming(request_body, send_result_to_task))
                task_id_mapping[chat_id] = task_id
                chat_id_mapping[unique_id] = chat_id
                background_tasks.add_task(
                    analytics_event,
                    analytics_service,
                    chat_id,
                    EventName.NEW_USER_CREATED,
                    req.headers,
                    request_body,
                    {},
                )
                background_tasks.add_task(
                    analytics_event,
                    analytics_service,
                    chat_id,
                    EventName.CONVERSATION_STARTED,
                    req.headers,
                    request_body,
                    {},
                )
                return {
                    "status_code": 200,
                    "message": "Message received successfully",
                    "chat_id": chat_id,
                }
    except Exception as e:
        logger.exception(
            f"Unhandled error processing /v3_async/initiate_chat: {e}",  # Contextual message
            data={"error_location": "/v3_async/initiate_chat"},
            status="FAILURE",
            # No need to manually add error string, formatter handles it
        )
        raise HTTPException(status_code=500, detail={"status": "Error occurred", "error": str(e)})


@router.post("/conversation/get_message", dependencies=[Depends(get_api_key)])
async def get_message(
    request_body: dict,
    req: Request,
    background_tasks: BackgroundTasks,
    analytics_service=Depends(get_analytics_service),
) -> dict:
    try:
        unique_id = request_body.get("user_info").get("user_id").get("id")
        chat_id = chat_id_mapping.get(unique_id)
        if not chat_id:
            logger.exception(
                "No active chat session found for chat_id. Provide initiate event",
                data={"error_location": "/v3_async/send_message"},
                status="FAILURE",
            )
            raise HTTPException(
                status_code=404,
                detail="No active chat session found for chat_id. Provide initiate event.",
            )
        set_context_variables(chat_id, unique_id, unique_id)
        for incoming_event in request_body.get("incoming_events", []):
            if incoming_event.get("event_template").get("event_type") in ["listen"]:
                task_id = task_id_mapping.get(chat_id)
                task_status = task_manager.get_status(task_id)
                if not task_status["done"]:
                    return {
                        "status_code": 200,
                        "message": "Agent generating response.",
                        "next_action": "wait",
                    }
                if task_status["result"]["should_end_interaction"] or task_status["result"]["next_action"] == "disconnect":
                    background_tasks.add_task(
                    analytics_event,
                    analytics_service,
                    chat_id,
                    EventName.CONVERSATION_ENDED,
                    req.headers,
                    {},
                    task_status["result"]
                )
                else:
                    background_tasks.add_task(
                        analytics_event,
                        analytics_service,
                        chat_id,
                        EventName.RESPONSE_SENT,
                        req.headers,
                        {},
                        task_status["result"]
                    )
                return task_status["result"]
    except Exception as e:
        logger.exception(
            f"Unhandled error processing /v3_async/send_message: {e}",  # Contextual message
            data={"error_location": "/v3_async/send_message"},
            status="FAILURE",
            # No need to manually add error string, formatter handles it
        )
        raise HTTPException(status_code=500, detail={"status": "Error occurred", "error": str(e)})


@router.post("/conversation/send_message", dependencies=[Depends(get_api_key)])
async def send_message(
    request_body: dict,
    req: Request,
    background_tasks: BackgroundTasks,
    analytics_service=Depends(get_analytics_service),
) -> dict:
    try:
        unique_id = request_body.get("user_info").get("user_id").get("id")
        chat_id = chat_id_mapping.get(unique_id)
        if not chat_id:
            logger.exception(
                "No active chat session found for chat_id. Provide initiate event",
                data={"error_location": "/v3_async/send_message"},
                status="FAILURE",
            )
            raise HTTPException(
                status_code=404,
                detail="No active chat session found for chat_id. Provide initiate event.",
            )
        set_context_variables(chat_id, unique_id, unique_id)
        for incoming_event in request_body.get("incoming_events", []):
            if incoming_event.get("event_template").get("event_type") in [
                "text",
                "contextkey",
            ]:
                task_id = task_manager.start_task()
                asyncio.create_task(build_and_run_pipeline_streaming(request_body, send_result_to_task))
                task_id_mapping[chat_id] = task_id
                background_tasks.add_task(
                    analytics_event,
                    analytics_service,
                    chat_id,
                    EventName.REQUEST_RECEIVED,
                    req.headers,
                    request_body,
                    {},
                )
                return {
                    "status_code": 200,
                    "message": "Message received successfully",
                    "chat_id": chat_id,
                }
    except Exception as e:
        logger.exception(
            f"Unhandled error processing /v3_async/send_message: {e}",  # Contextual message
            data={"error_location": "/v3_async/send_message"},
            status="FAILURE",
            # No need to manually add error string, formatter handles it
        )
        raise HTTPException(status_code=500, detail={"status": "Error occurred", "error": str(e)})


@router.post("/conversation/end_chat", dependencies=[Depends(get_api_key)])
async def end_chat(
    request_body: dict,
    req: Request,
    background_tasks: BackgroundTasks,
    analytics_service=Depends(get_analytics_service),
) -> dict:
    try:
        unique_id = request_body.get("user_info").get("user_id").get("id")
        chat_id = chat_id_mapping.get(unique_id)
        if not chat_id:
            logger.exception(
                "No active chat session found for chat_id. Provide initiate event",
                data={"error_location": "/v3_async/send_message"},
                status="FAILURE",
            )
            raise HTTPException(
                status_code=404,
                detail="No active chat session found for chat_id. Provide initiate event.",
            )
        for incoming_event in request_body.get("incoming_events", []):
            if incoming_event.get("event_template").get("event_type") in ["disconnect"]:
                await build_and_run_pipeline_shutdown()
                set_context_variables(chat_id, unique_id, unique_id)
                background_tasks.add_task(
                    analytics_event,
                    analytics_service,
                    chat_id,
                    EventName.CONVERSATION_ENDED,
                    req.headers,
                    {},
                    {},
                )
                del chat_id_mapping[unique_id]
                del task_id_mapping[chat_id]

        return {
            "status_code": 200,
            "message": "Chat ended successfully",
            "chat_id": chat_id,
        }
    except Exception as e:
        logger.exception(
            f"Unhandled error processing /v3_async/end_chat: {e}",  # Contextual message
            data={"error_location": "/v3_async/end_chat"},
            status="FAILURE",
            # No need to manually add error string, formatter handles it
        )
        raise HTTPException(status_code=500, detail={"status": "Error occurred", "error": str(e)})
