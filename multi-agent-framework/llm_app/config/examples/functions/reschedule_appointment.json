{"function_id": "reschedule_appointment", "code": "def reschedule_appointment(\n    branch_name: Annotated[str, \"The name of the branch where the appointments are to be rescheduled. This is a required field.\"],\n    phone_number: Annotated[str, \"The phone number of the client. This is a required field.\"],\n    appointments: Annotated[list, \"A list of appointments to reschedule. Each appointment should be a dictionary containing appointment_id (The unique identifier ID of the appointment that needs to be rescheduled), appointment_date (The new date for the rescheduled appointment in YYYY-MM-DD format), start_time (The new start time for the rescheduled appointment in HH:MM:SS format), and staff_name (The name of the staff member who will provide the service). At least one appointment is required.\"],\n    note: Annotated[str, \"Optional notes or comments for the rescheduled appointments.\"] = \"\",\n    context_arguments: dict = {}\n) -> str:\n    \"\"\"\n    Reschedules one or multiple existing appointments with new date, time and optionally different staff member.\n    \n    Args:\n        branch_name: The salon branch identifier\n        phone_number: Client's phone number\n        appointments: List of appointments to reschedule, each containing:\n            - appointment_id: The existing appointment identifier\n            - appointment_date: New appointment date (YYYY-MM-DD)\n            - start_time: New appointment time (HH:MM:SS)\n            - staff_name: Staff member name\n        note: Optional appointment notes\n        context_arguments: Additional context parameters including chat_id\n        \n    Returns:\n        str: JSON string containing the rescheduling response or error message\n    \"\"\"\n    body = {\n        \"business_id\": \"Wj6YQfGEsvSqkLskUdhu3g\",\n        \"branch_name\": branch_name,\n        \"chat_id\": context_arguments.get(\"chat_id\"),\n        \"reschedule_appointment_config\": appointments,\n        \"phone_number\": phone_number,\n        \"note\": note\n    }\n\n    url = \"https://brook.dev-scc-demo.rozie.ai/phorest/reschedule_appointment\"\n    headers = {\"Content-Type\": \"application/json\"}\n    param = {\n        \"url\": url,\n        \"timeout\": 30,\n        \"headers\": headers,\n    }\n\n    if body:\n        param[\"data\"] = json.dumps(body)\n        try:\n            response = requests.post(**param)\n            if response.status_code != 200:\n                return \"Failed\"\n            response_json = response.json()\n            return json.dumps(response_json, indent=2)\n        except Exception as e:\n            return \"Failed\", str(e)\n", "description": "The reschedule_appointment function allows users to reschedule one or multiple existing appointments to new dates and times, optionally with different staff members. To use it, you must provide the following required parameters: branch_name, which is the name of the branch where the appointments were booked; phone_number, which is the client's phone number for verification; and appointments, which is a list of appointments to reschedule. Each appointment in the list must include: appointment_id (the unique identifier of the existing appointment), appointment_date (the new desired date in YYYY-MM-DD format), start_time (the new desired start time in HH:MM:SS format), and staff_name (the name of the staff member who will provide the service). An optional parameter is note, which allows for additional comments or notes for all the rescheduled appointments.", "function_name": "reschedule_appointment", "roster_id": ""}