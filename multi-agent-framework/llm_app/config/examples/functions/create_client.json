{"function_id": "create_client", "code": "def create_client(\n    phone_number: Annotated[str, \"The phone number of the user/client.\"],\n    first_name: Annotated[str, \"The first name of the user/client.\"],\n    last_name: Annotated[str, \"The last name of the user/client.\"],\n    context_arguments: dict = {} \n)-> str:\n    \"\"\"Generic POST request handler for Phorest API interactions.\"\"\"\n    body = {\n        \"business_id\": \"Wj6YQfGEsvSqkLskUdhu3g\",\n        \"phone_number\": phone_number,\n        \"client_first_name\": first_name,\n        \"client_last_name\": last_name,\n        \"chat_id\": context_arguments.get(\"chat_id\"),\n\t}\n    url = \"https://brook.dev-scc-demo.rozie.ai/phorest/create_client_account\"\n    headers = {\"Content-Type\": \"application/json\"}\n    param = {\n            \"url\": url,\n            \"timeout\": 30,\n            \"headers\": headers,\n        }\n    if body:\n        param[\"data\"] = json.dumps(body)\n        try:\n            response = requests.post(**param)\n            if response.status_code != 200:\n                return \"Failed\"\n            response_json = response.json()\n            return json.dumps(response_json, indent=2)\n        except Exception as e:\n            return \"Failed\", str(e)", "description": "The create_client function allows you to add a new client by providing their phone number, first name and last_name.", "function_name": "create_client", "roster_id": ""}