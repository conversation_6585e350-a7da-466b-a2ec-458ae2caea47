{"function_id": "get_stylist_time_availability", "code": "def get_stylist_time_availability(\n    Salons_Branch_Name: Annotated[str, \"This parameter specifies the location of the selected salon branch.\"],\n    Salon_Service: Annotated[list, \"A list of salon services selected by the customer. This will filter salon locations based on the services offered at each location.\"],\n    Preferred_Date: Annotated[str, \"Customer preferred date for appointment in `%Y-%m-%d` format strictly.\"] ,\n    Stylist_Name: Annotated[str, \"An stylist full name to filter based on stylist availability\"] = \"\",\n   Preferred_Time: Annotated[str, \"Customer preferred time for appointment in `HH:MM:SS` format strictly.\"] = \"\",\n    context_arguments: dict = {}  \n) -> str:  \n    body = {\n    \"business_id\": \"Wj6YQfGEsvSqkLskUdhu3g\",\n    \"chat_id\": context_arguments.get(\"chat_id\"),\n    \"Salons_Branch_Name\": Salons_Branch_Name,\n    \"Salon_Service\": Salon_Service,\n    \"Stylist_Name\": Stylist_Name,\n    \"Preferred_Date\": Preferred_Date,\n    \"Preferred_Time\": Preferred_Time\n\t}  \n    url = \"http://brook.dev-scc-demo.rozie.ai/phorest/get_available_slots\" \n    headers = {\"Content-Type\": \"application/json\"}\n    param = {\n        \"url\": url,\n        \"timeout\": 30,\n        \"headers\": headers,\n    }\n    if body:\n        param[\"data\"] = json.dumps(body)\n    try:\n        response = requests.post(**param)\n        print(\"response:\", response.text)\n        if response.status_code not in [200, 201, 202]:\n            if response.json():\n                return \"Failed\"\n            return \"Failed\"\n        response_data = response.json()\n    except Exception as e:\n        print(\"Error in get_stylist_time_availability :\", e)\n        return f\"Failed {str(e)}\"\n\n    return json.dumps(response_data, indent=2)\n", "description": "To check the availability of stylists, use this function which retrieve a list of available appointment slots. If you want to view slots for a specific stylist, apply the Stylist_Name filter, ensuring the results are limited to that stylist. The Stylist_Name should be a valid full name obtained through the get_salon_stylists function. Additionally, you can use the Preferred_Date filter to specify a particular date for which you wish to check availability. The function returns a comprehensive list of appointment slots, each providing details such as the start time, end time, stylist's name, and the schedule for every service. This makes it easier to identify and book suitable appointments tailored to your preferences and availability.", "function_name": "get_stylist_time_availability"}