# This file was auto-generated by Fern from our API Definition.

import datetime as dt
import typing

from ....core.datetime_utils import serialize_datetime
from ....core.pydantic_utilities import deep_union_pydantic_dicts, pydantic_v1
from .map_value import MapValue
from .observation_level import ObservationLevel
from .usage import Usage


class Observation(pydantic_v1.BaseModel):
    id: str = pydantic_v1.Field()
    """
    The unique identifier of the observation
    """

    trace_id: typing.Optional[str] = pydantic_v1.Field(alias="traceId", default=None)
    """
    The trace ID associated with the observation
    """

    type: str = pydantic_v1.Field()
    """
    The type of the observation
    """

    name: typing.Optional[str] = pydantic_v1.Field(default=None)
    """
    The name of the observation
    """

    start_time: dt.datetime = pydantic_v1.Field(alias="startTime")
    """
    The start time of the observation
    """

    end_time: typing.Optional[dt.datetime] = pydantic_v1.Field(alias="endTime", default=None)
    """
    The end time of the observation.
    """

    completion_start_time: typing.Optional[dt.datetime] = pydantic_v1.Field(alias="completionStartTime", default=None)
    """
    The completion start time of the observation
    """

    model: typing.Optional[str] = pydantic_v1.Field(default=None)
    """
    The model used for the observation
    """

    model_parameters: typing.Optional[typing.Dict[str, MapValue]] = pydantic_v1.Field(
        alias="modelParameters", default=None
    )
    """
    The parameters of the model used for the observation
    """

    input: typing.Optional[typing.Any] = pydantic_v1.Field(default=None)
    """
    The input data of the observation
    """

    version: typing.Optional[str] = pydantic_v1.Field(default=None)
    """
    The version of the observation
    """

    metadata: typing.Optional[typing.Any] = pydantic_v1.Field(default=None)
    """
    Additional metadata of the observation
    """

    output: typing.Optional[typing.Any] = pydantic_v1.Field(default=None)
    """
    The output data of the observation
    """

    usage: typing.Optional[Usage] = pydantic_v1.Field(default=None)
    """
    The usage data of the observation
    """

    level: ObservationLevel = pydantic_v1.Field()
    """
    The level of the observation
    """

    status_message: typing.Optional[str] = pydantic_v1.Field(alias="statusMessage", default=None)
    """
    The status message of the observation
    """

    parent_observation_id: typing.Optional[str] = pydantic_v1.Field(alias="parentObservationId", default=None)
    """
    The parent observation ID
    """

    prompt_id: typing.Optional[str] = pydantic_v1.Field(alias="promptId", default=None)
    """
    The prompt ID associated with the observation
    """

    def json(self, **kwargs: typing.Any) -> str:
        kwargs_with_defaults: typing.Any = {"by_alias": True, "exclude_unset": True, **kwargs}
        return super().json(**kwargs_with_defaults)

    def dict(self, **kwargs: typing.Any) -> typing.Dict[str, typing.Any]:
        kwargs_with_defaults_exclude_unset: typing.Any = {"by_alias": True, "exclude_unset": True, **kwargs}
        kwargs_with_defaults_exclude_none: typing.Any = {"by_alias": True, "exclude_none": True, **kwargs}

        return deep_union_pydantic_dicts(
            super().dict(**kwargs_with_defaults_exclude_unset), super().dict(**kwargs_with_defaults_exclude_none)
        )

    class Config:
        frozen = True
        smart_union = True
        allow_population_by_field_name = True
        populate_by_name = True
        extra = pydantic_v1.Extra.allow
        json_encoders = {dt.datetime: serialize_datetime}
