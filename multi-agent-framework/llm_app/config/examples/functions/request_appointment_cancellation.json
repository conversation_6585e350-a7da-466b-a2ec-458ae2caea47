{"function_id": "request_appointment_cancellation", "code": "def request_appointment_cancellation(\n    branch_name: Annotated[str, \"The name of the branch where the appointments were booked. All appointment_ids must belong to this branch.\"],\n    appointment_ids: Annotated[list, \"A list of dictionaries, where each dictionary contains 'appointment_id' and 'time' for the appointment to be cancelled . Time attribute's value must strictly follow `%B %d, %Y, %I:%M %p` Each appointment ID must belong to the provided branch_name, and all must align with the same booking context.\"],\n    appointments_details: Annotated[list, \"A list of dictionaries containing detailed information about each appointment to be cancelled. Each dictionary must correspond to an appointment ID in the appointment_ids list.\"],\n\n    context_arguments: dict = {} \n)-> str:\n    \"\"\"Generic POST request handler for Phorest API interactions.\"\"\"\n    body = {\n        \"business_id\": \"Wj6YQfGEsvSqkLskUdhu3g\",\n        \"branch_name\": branch_name,\n        \"appointment_ids\": appointment_ids,\n        \"appointments_details\": appointments_details,\n        \"chat_id\": context_arguments.get(\"chat_id\"),\n\t}\n    url = \"https://brook.dev-scc-demo.rozie.ai/phorest/cancel_appointment_requests\"\n    headers = {\"Content-Type\": \"application/json\"}\n    param = {\n            \"url\": url,\n            \"timeout\": 30,\n            \"headers\": headers,\n        }\n    if body:\n        param[\"data\"] = json.dumps(body)\n        try:\n            response = requests.post(**param)\n            if response.status_code != 200:\n                return \"Failed\"\n            response_json = response.json()\n            return json.dumps(response_json, indent=2)\n        except Exception as e:\n            return \"Failed\", str(e)\n", "description": "The request_appointment_cancellation function lets users cancel their booked appointments. To use it, you must provide the following: branch_name, which is the name of the branch where the appointments were booked; appointment_ids, a list of dictionaries where each dictionary contains ‘appointment_id’ and ‘time’ for the appointments to cancel. time attribute's value must strictly follow `%B %d, %Y, %I:%M %p` ; and appointments_details, a list of dictionaries with details for each appointment, including appointmentId, staffName, serviceName, branchName, and time. Each dictionary must match an ID in the appointment_ids list. This function handles cancellations for one branch at a time, so all appointment_ids must belong to the given branch_name. All three parameters are required for the function to work correctly.", "function_name": "request_appointment_cancellation", "roster_id": ""}