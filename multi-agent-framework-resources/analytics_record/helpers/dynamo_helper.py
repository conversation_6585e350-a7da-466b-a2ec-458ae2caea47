import logging
import boto3
from botocore.exceptions import ClientError
from boto3.dynamodb.conditions import Attr, Key
from decimal import Decimal

# Initialize DynamoDB client and resource
dynamodb_client = boto3.client("dynamodb")
dynamodb_resource = boto3.resource("dynamodb")


def parse_dict(data):
    if isinstance(data, dict):
        return {k: parse_dict(v) for k, v in data.items()}
    elif isinstance(data, list):
        return [parse_dict(item) for item in data]
    elif isinstance(data, Decimal):
        return float(data)
    else:
        return data


def dynamo_client_operation(operation, paginate=False, **kwargs):
    """
    Perform a DynamoDB operation using the client with the provided parameters.

    Parameters:
    - operation (str): The DynamoDB operation to perform (e.g., 'scan', 'get_item', 'put_item', etc.)
    - paginate (bool): Whether to handle pagination.
    - kwargs: Additional keyword arguments to pass to the DynamoDB operation.

    Returns:
    - dict or list: The response from the DynamoDB operation. The return type depends on the operation.
    """
    try:
        func = getattr(dynamodb_client, operation)

        if paginate:
            paginator = dynamodb_client.get_paginator(operation)
            response_iterator = paginator.paginate(**kwargs)
            items = []
            for page in response_iterator:
                items.extend(page.get("Items", []))
            logging.info(f"Paginated response from {operation}: {items}")
            return items
        else:
            response = func(**kwargs)
            logging.info(f"Response from {operation}: {response}")
            return response

    except ClientError as e:
        logging.error(f"ClientError in {operation}: {e}")
        raise e
    except Exception as e:
        logging.error(f"Unexpected error in {operation}: {e}")
        raise e


# Initialize DynamoDB resource
dynamodb_resource = boto3.resource("dynamodb")


def dynamo_resource_operation(
    operation,
    table_name,
    paginate=False,
    key_condition=None,
    filter_dict=None,
    projection_expression=None,
    **kwargs,
):
    """
    Perform a DynamoDB operation using the resource with the provided parameters.

    Parameters:
    - operation (str): The DynamoDB operation to perform (e.g., 'scan', 'query', 'get_item', etc.)
    - table_name (str): The name of the DynamoDB table.
    - paginate (bool): Whether to handle pagination (relevant for 'scan' operations).
    - key_condition (dict, optional): A dictionary specifying the primary key conditions for 'query' operations.
    - filter_dict (dict, optional): A dictionary of additional filter conditions for 'scan' or 'query' operations.
    - projection_expression (str, optional): A string specifying the attributes to retrieve.
    - kwargs: Additional keyword arguments to pass to the DynamoDB operation.

    Returns:
    - dict or list: The response from the DynamoDB operation. The return type depends on the operation.
    """
    try:
        table = dynamodb_resource.Table(table_name)
        func = getattr(table, operation)

        # Build the key condition expression if key_condition is provided
        if operation == "query" and key_condition:
            key_condition_expression = Key(list(key_condition.keys())[0]).eq(list(key_condition.values())[0])
            kwargs["KeyConditionExpression"] = key_condition_expression

        # Build the filter expression if filter_dict is provided
        if filter_dict:
            filter_expression = None
            for key, (operator, value) in filter_dict.items():
                condition = getattr(Attr(key), operator)(value)
                if filter_expression is None:
                    filter_expression = condition
                else:
                    filter_expression &= condition
            kwargs["FilterExpression"] = filter_expression

        if projection_expression:
            kwargs["ProjectionExpression"] = projection_expression

        if paginate and operation == "scan":
            scan_kwargs = kwargs
            items = []
            done = False
            start_key = None

            while not done:
                if start_key:
                    scan_kwargs["ExclusiveStartKey"] = start_key

                response = func(**scan_kwargs)
                items.extend(response.get("Items", []))
                start_key = response.get("LastEvaluatedKey", None)
                done = start_key is None

            # logging.info(f"Paginated response from {operation}: {items}")
            return parse_dict(items)
        if operation == "put_item":
            response = func(**kwargs)
            logging.info(f"Response from {operation}: {response}")
            return response
        else:
            response = func(**kwargs)
            # logging.info(f"Response from {operation}: {response}")
            if response.get("Items"):
                return parse_dict(response["Items"])
            else:
                return parse_dict(response["Item"])

    except ClientError as e:
        # logging.error(f"ClientError in {operation}: {e}")
        raise e
    except Exception as e:
        # logging.error(f"Unexpected error in {operation}: {e}")
        raise e
