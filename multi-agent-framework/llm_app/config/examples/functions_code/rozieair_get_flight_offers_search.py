import json
import requests
from datetime import datetime
from typing_extensions import Annotated

def rozieair_get_flight_offers_search(
    origin: Annotated[str, "Origin airport code (e.g., PAR)"],
    destination: Annotated[str, "Destination airport code (e.g., ICN)"],
    departure_date: Annotated[str, "Departure date (YYYY-MM-DD)"],
    return_date: Annotated[str, "Return date for round trips (YYYY-MM-DD)"] = None,
    adults: Annotated[int, "Number of adult passengers"] = 1,
    children: Annotated[int, "Number of child passengers"] = 0,
    infants: Annotated[int, "Number of infant passengers"] = 0,
    context_arguments: dict = {}
) -> dict:
    """Search for flight offers using the flight offers search API."""
    query_params = {
        "origin": origin.upper(),
        "destination": destination.upper(),
        "departure_date": departure_date,
        "adults": adults,
        "children": children,
        "infants": infants,
        "max_offers": 3
    }

    if return_date:
        query_params["return_date"] = return_date

    url = "https://rozie-amadeus.dev-scc-demo.rozie.ai/api/v1/flight-offers-search"
    headers = {"accept": "application/json"}

    try:
        response = requests.get(url, headers=headers, params=query_params, timeout=30)
        if response.status_code != 200:
            return {
                "llm_result": {"error": f"Flight offer API error: {response.status_code}"},
                "custom_results": []
            }

        result = response.json()

        # Process flight offers for template
        processed_offers = []
        for idx, offer in enumerate(result.get("data", []), 1):
            price = offer.get("price", {}).get("total", "0.00")
            currency = offer.get("price", {}).get("currency", "EUR")
            itineraries = offer.get("itineraries", [])

            if not itineraries:
                continue

            outbound = itineraries[0]
            return_trip = itineraries[1] if len(itineraries) > 1 else None

            # Process outbound segments
            outbound_segments = []
            for segment in outbound.get("segments", []):
                departure = segment.get("departure", {})
                arrival = segment.get("arrival", {})

                # Format datetime strings
                try:
                    departure_time = datetime.fromisoformat(departure.get("at", "")).strftime("%b %d, %Y – %H:%M")
                except Exception:
                    departure_time = departure.get("at", "") or "N/A"

                try:
                    arrival_time = datetime.fromisoformat(arrival.get("at", "")).strftime("%b %d, %Y – %H:%M")
                except Exception:
                    arrival_time = arrival.get("at", "") or "N/A"

                flight_number = f"{segment.get('carrierCode', '')} {segment.get('number', '')}".strip()
                aircraft = segment.get("aircraft", {}).get("code", "")

                outbound_segments.append({
                    "departure_code": departure.get("iataCode", ""),
                    "departure_time": departure_time,
                    "arrival_code": arrival.get("iataCode", ""),
                    "arrival_time": arrival_time,
                    "flight_number": flight_number,
                    "aircraft": aircraft
                })

            # Process return segments if exists
            return_segments = []
            if return_trip:
                for segment in return_trip.get("segments", []):
                    departure = segment.get("departure", {})
                    arrival = segment.get("arrival", {})

                    try:
                        departure_time = datetime.fromisoformat(departure.get("at", "")).strftime("%b %d, %Y – %H:%M")
                    except Exception:
                        departure_time = departure.get("at", "") or "N/A"

                    try:
                        arrival_time = datetime.fromisoformat(arrival.get("at", "")).strftime("%b %d, %Y – %H:%M")
                    except Exception:
                        arrival_time = arrival.get("at", "") or "N/A"

                    flight_number = f"{segment.get('carrierCode', '')} {segment.get('number', '')}".strip()
                    aircraft = segment.get("aircraft", {}).get("code", "")

                    return_segments.append({
                        "departure_code": departure.get("iataCode", ""),
                        "departure_time": departure_time,
                        "arrival_code": arrival.get("iataCode", ""),
                        "arrival_time": arrival_time,
                        "flight_number": flight_number,
                        "aircraft": aircraft
                    })

            # Calculate summary info
            origin_code = outbound["segments"][0].get("departure", {}).get("iataCode", "")
            dest_code = outbound["segments"][-1].get("arrival", {}).get("iataCode", "")
            outbound_duration = outbound.get("duration", "PT0H0M").replace("PT", "").replace("H", "h ").replace("M", "m")
            return_duration = return_trip.get("duration", "PT0H0M").replace("PT", "").replace("H", "h ").replace("M", "m") if return_trip else ""
            stops_count = max(len(outbound.get("segments", [])) - 1, 0)
            stops_str = f"{stops_count} Stop" + ("" if stops_count == 1 else "s")

            processed_offers.append({
                "index": idx,
                "offer_id": offer.get("id", ""),
                "price": price,
                "currency": currency,
                "origin_code": origin_code,
                "dest_code": dest_code,
                "outbound_duration": outbound_duration,
                "return_duration": return_duration,
                "stops_str": stops_str,
                "outbound_segments": outbound_segments,
                "return_segments": return_segments,
                "has_return": bool(return_trip),
                "is_first": idx == 1
            })

        return {
            "llm_result": result,
            "offers": processed_offers,
            "search_params": {
                "origin": origin.upper(),
                "destination": destination.upper(),
                "departure_date": departure_date,
                "return_date": return_date,
                "adults": adults,
                "children": children,
                "infants": infants
            }
        }


        
    except Exception as e:
        return {
            "llm_result": {"error": str(e)},
            "custom_results": []
        }

# Example usage
if __name__ == "__main__":
    result = rozieair_get_flight_offers_search(
        origin="LON",
        destination="JFK",
        departure_date="2025-05-25",
        return_date="2025-05-30",
        adults=1,
        children=0,
        infants=0
    )
    print(json.dumps(result, indent=2))