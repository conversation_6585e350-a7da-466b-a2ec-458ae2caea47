# This file was auto-generated by Fern from our API Definition.

import datetime as dt
import typing

from ....core.datetime_utils import serialize_datetime
from ....core.pydantic_utilities import deep_union_pydantic_dicts, pydantic_v1


class CreateDatasetRunItemRequest(pydantic_v1.BaseModel):
    run_name: str = pydantic_v1.Field(alias="runName")
    run_description: typing.Optional[str] = pydantic_v1.Field(alias="runDescription", default=None)
    """
    Description of the run. If run exists, description will be updated.
    """

    metadata: typing.Optional[typing.Any] = pydantic_v1.Field(default=None)
    """
    Metadata of the dataset run, updates run if run already exists
    """

    dataset_item_id: str = pydantic_v1.Field(alias="datasetItemId")
    observation_id: typing.Optional[str] = pydantic_v1.Field(alias="observationId", default=None)
    trace_id: typing.Optional[str] = pydantic_v1.Field(alias="traceId", default=None)
    """
    traceId should always be provided. For compatibility with older SDK versions it can also be inferred from the provided observationId.
    """

    def json(self, **kwargs: typing.Any) -> str:
        kwargs_with_defaults: typing.Any = {"by_alias": True, "exclude_unset": True, **kwargs}
        return super().json(**kwargs_with_defaults)

    def dict(self, **kwargs: typing.Any) -> typing.Dict[str, typing.Any]:
        kwargs_with_defaults_exclude_unset: typing.Any = {"by_alias": True, "exclude_unset": True, **kwargs}
        kwargs_with_defaults_exclude_none: typing.Any = {"by_alias": True, "exclude_none": True, **kwargs}

        return deep_union_pydantic_dicts(
            super().dict(**kwargs_with_defaults_exclude_unset), super().dict(**kwargs_with_defaults_exclude_none)
        )

    class Config:
        frozen = True
        smart_union = True
        allow_population_by_field_name = True
        populate_by_name = True
        extra = pydantic_v1.Extra.allow
        json_encoders = {dt.datetime: serialize_datetime}
